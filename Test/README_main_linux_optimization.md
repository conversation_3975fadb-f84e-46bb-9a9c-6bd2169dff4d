# main_linux.cpp 优化说明

## 优化概述

原始的 `main_linux.cpp` 文件有752行代码，包含大量重复代码、冗余函数和不必要的复杂性。优化后的版本减少到436行，提高了代码质量和可维护性。

## 主要优化内容

### 1. **代码结构优化**
- **消除重复包含**: 移除了重复的头文件包含和宏定义
- **统一命名空间**: 使用 `std::` 前缀，避免 `using namespace std`
- **模块化设计**: 将功能拆分为独立的函数和类

### 2. **内存管理优化**
- **RAII 设计**: 实现 `ImageBuffer` 类自动管理内存
- **移动语义**: 支持 C++11 移动构造和移动赋值
- **异常安全**: 防止内存泄漏

```cpp
class ImageBuffer {
public:
    explicit ImageBuffer(size_t size);
    ~ImageBuffer();
    ImageBuffer(ImageBuffer&& other) noexcept;  // 移动构造
    ImageBuffer& operator=(ImageBuffer&& other) noexcept;  // 移动赋值
    // 禁用拷贝构造和拷贝赋值
    ImageBuffer(const ImageBuffer&) = delete;
    ImageBuffer& operator=(const ImageBuffer&) = delete;
};
```

### 3. **pPlanePtr 赋值优化**
完全参考 `main.cpp` 的实现逻辑：

#### 普通图像文件（BGR格式）- GPU模式逻辑
```cpp
// 参考main.cpp第521-522行
image.imgData.pPlanePtr[0] = buffer.get();  // pPlanePtr[0] = ImgData
image.imgData.pPlanePtr[1] = nullptr;       // pPlanePtr[1] = NULL
```

#### YUV格式文件（NV21/NV12）- CPU模式逻辑
```cpp
// 参考main.cpp第642-643行
image.imgData.pPlanePtr[0] = buffer.get();  // pPlanePtr[0] = yuvData
image.imgData.pPlanePtr[1] = buffer.get() + stride * height;  // pPlanePtr[1] = yuvData + nStride * nHeight
```

### 4. **函数优化**

#### 图像加载函数
- `load_image_file()`: 处理 JPG/PNG/BMP 等格式
- `load_yuv_file()`: 处理 NV21/NV12 原始格式
- `swap_rgb_channels()`: RGB/BGR 转换优化

#### 元数据设置
```cpp
static void set_image_metadata(BSTImage& image, int rotation, float zoom) {
    // 参考main.cpp第525-529行和第646-649行的逻辑
    image.metaData.nRotation = static_cast<BSTRotation>(rotation);
    image.metaData.nZoom = zoom;
    image.metaData.nDacVal = 0;  // focal distance
    image.metaData.nFace.m_FaceNum = 0;
}
```

### 5. **配置管理优化**
使用结构体统一管理配置参数：

```cpp
struct Config {
    std::string imgdir, cfgdir, modeldir;
    int selectMode = 0;
    int nCameraPos = 0;
    int nWidth = 640, nHeight = 640, nStride = 640;
    float zoom = 1.0f;
};
```

### 6. **错误处理优化**
- **统一错误检查**: 使用 `CHECK_ERROR` 宏
- **RAII 资源管理**: 自动清理资源
- **异常安全**: 防止资源泄漏

### 7. **性能优化**
- **减少内存分配**: 使用移动语义避免不必要的拷贝
- **算法优化**: 使用 STL 算法提高效率
- **编译优化**: 支持 C++11 特性

## 兼容性保证

### 与 main.cpp 的兼容性
- **完全兼容**: pPlanePtr 赋值逻辑完全参考 main.cpp
- **参数兼容**: 命令行参数与原版本完全一致
- **功能兼容**: 支持所有原有功能

### 编译兼容性
- **C++11 标准**: 使用现代 C++ 特性
- **跨平台**: 支持 Linux 系统
- **依赖最小**: 只依赖必要的系统库

## 使用方法

编译：
```bash
g++ -I../AiScene -std=c++11 -c main_linux.cpp -o main_linux.o
```

运行：
```bash
./main_linux 2 -ImageDir ./images -Config ./config.cfg -model ./models
```

## 代码质量提升

1. **可读性**: 清晰的函数命名和注释
2. **可维护性**: 模块化设计，易于修改和扩展
3. **可测试性**: 独立的函数便于单元测试
4. **安全性**: RAII 和异常安全设计
5. **效率**: 现代 C++ 特性提升性能

## 总结

优化后的 `main_linux.cpp` 在保持完全功能兼容的前提下，显著提升了代码质量：
- **代码行数**: 从 752 行减少到 436 行（减少 42%）
- **内存安全**: RAII 自动内存管理
- **性能提升**: 现代 C++ 特性优化
- **可维护性**: 清晰的模块化结构
- **兼容性**: 完全参考 main.cpp 的 pPlanePtr 赋值逻辑
