#include "clipp.h"
#include "../AiScene/BSTAIScene.h"
#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
#include <fstream>
#include <cstdio>
#include <cstring>
#include <dirent.h>
#include <sys/stat.h>
#include <memory>
#include <map>
#include <sstream>
#include <iomanip>

#ifdef AISCENE_USE_OPENCV
#include <opencv2/imgproc.hpp>
#include <opencv2/imgcodecs.hpp>
#endif

using clipp::group;
using clipp::value;
using clipp::option;
using clipp::required;
using clipp::opt_value;

// 外部函数声明
extern int BSTAISceneRenderV2Ext(BHANDLE handle, BSTImage* mainImg, int* OutNum, 
                                int AIType[MAX_OUT_NUM], float AIConf[MAX_OUT_NUM], float Area[MAX_OUT_NUM]);

// 错误检查宏
#define CHECK_ERROR(x) do { \
    int result = (x); \
    if (result != BST_AISCENE_SUCCESS) { \
        std::printf("Error: code = %d at line %d\n", result, __LINE__); \
    } \
} while(0)

// RAII 内存管理类
class ImageBuffer {
public:
    explicit ImageBuffer(size_t size) : data_(static_cast<unsigned char*>(std::malloc(size))), size_(size) {}
    ~ImageBuffer() { if (data_) std::free(data_); }
    
    ImageBuffer(const ImageBuffer&) = delete;
    ImageBuffer& operator=(const ImageBuffer&) = delete;
    
    ImageBuffer(ImageBuffer&& other) noexcept : data_(other.data_), size_(other.size_) {
        other.data_ = nullptr;
        other.size_ = 0;
    }
    
    ImageBuffer& operator=(ImageBuffer&& other) noexcept {
        if (this != &other) {
            if (data_) std::free(data_);
            data_ = other.data_;
            size_ = other.size_;
            other.data_ = nullptr;
            other.size_ = 0;
        }
        return *this;
    }
    
    unsigned char* get() const { return data_; }
    size_t size() const { return size_; }
    bool valid() const { return data_ != nullptr; }
    
private:
    unsigned char* data_;
    size_t size_;
};

// 图像文件列表获取
static std::vector<std::string> list_image_files(const std::string& directory) {
    std::vector<std::string> results;
    
    DIR* dir = opendir(directory.c_str());
    if (!dir) return results;
    
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        if (entry->d_type == DT_REG) {
            std::string filename = entry->d_name;
            std::string ext = filename.substr(filename.find_last_of('.'));
            std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
            
            if (ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".bmp" || 
                ext == ".nv21" || ext == ".nv12") {
                results.emplace_back(directory + "/" + filename);
            }
        }
    }
    
    closedir(dir);
    std::sort(results.begin(), results.end());
    return results;
}

// RGB/BGR 转换
static void swap_rgb_channels(unsigned char* data, int width, int height) {
    for (int i = 0; i < width * height; ++i) {
        std::swap(data[i * 3], data[i * 3 + 2]);
    }
}

// 设置图像元数据 - 参考main.cpp的metaData赋值逻辑
static void set_image_metadata(BSTImage& image, int rotation, float zoom) {
    image.metaData.nRotation = static_cast<BSTRotation>(rotation);  // 参考main.cpp第525行和第646行
    image.metaData.nZoom = zoom;                                    // 参考main.cpp第527行和第648行
    image.metaData.nDacVal = 0;                                     // 参考main.cpp第526行和第647行: focal distance
    image.metaData.nFace.m_FaceNum = 0;                            // 参考main.cpp第528行和第649行
    image.metaData.nGain = 1.0f;
    image.metaData.nExposure = 0;
    image.metaData.nISO = 0;
    image.metaData.nGyro[0] = 0.0f;
    image.metaData.nGyro[1] = 0.0f;
    image.metaData.nGyro[2] = 0.0f;
}

// 从普通图像文件加载BSTImage - 参考main.cpp GPU模式的pPlanePtr赋值
static bool load_image_file(const std::string& file_path, BSTImage& image, ImageBuffer& buffer) {
    int w = 0, h = 0, c = 0;
    unsigned char* data = BSTLoadImage(file_path.c_str(), &w, &h, &c);
    if (!data || w <= 0 || h <= 0) {
        if (data) std::free(data);
        return false;
    }
    
    // 创建缓冲区并复制数据
    buffer = ImageBuffer(w * h * c);
    if (!buffer.valid()) {
        std::free(data);
        return false;
    }
    
    std::memcpy(buffer.get(), data, w * h * c);
    std::free(data);
    
    // 设置图像格式
    if (c == 3) {
        swap_rgb_channels(buffer.get(), w, h);  // 参考main.cpp的swap_rgb调用
        image.imgData.nFormat = BST_IMAGE_TYPE_BGR;
    } else if (c == 1) {
        image.imgData.nFormat = BST_IMAGE_TYPE_GRAY;
    } else {
        return false;
    }
    
    // 设置图像数据 - 对于BGR图像参考main.cpp GPU模式逻辑 (第521-522行)
    image.imgData.nWidth = static_cast<BSIZE>(w);
    image.imgData.nHeight = static_cast<BSIZE>(h);
    image.imgData.pPlanePtr[0] = buffer.get();  // 参考main.cpp第521行: pPlanePtr[0] = ImgData
    image.imgData.pPlanePtr[1] = nullptr;       // 参考main.cpp第522行: pPlanePtr[1] = NULL (BGR格式)
    image.imgData.pPlanePtr[2] = nullptr;
    image.imgData.pPlanePtr[3] = nullptr;
    image.imgData.nRowPitch[0] = static_cast<BSIZE>(w);  // 参考main.cpp第523行: nRowPitch[0] = nStride
    image.imgData.nRowPitch[1] = static_cast<BSIZE>(w);  // 参考main.cpp第524行: nRowPitch[1] = nStride
    image.imgData.nRowPitch[2] = 0;
    image.imgData.nRowPitch[3] = 0;
    image.imgData.nTextureID = 0;
    
    return true;
}

// 从YUV文件加载BSTImage - 参考main.cpp CPU模式的pPlanePtr赋值
static bool load_yuv_file(const std::string& file_path, int width, int height, int stride,
                         BSTImageType format, BSTImage& image, ImageBuffer& buffer) {
    if (width <= 0 || height <= 0) return false;
    if (stride <= 0) stride = width;
    
    const size_t expected = static_cast<size_t>(stride) * static_cast<size_t>(height) * 3 / 2;
    std::ifstream ifs(file_path, std::ios::binary);
    if (!ifs) return false;
    
    buffer = ImageBuffer(expected);
    if (!buffer.valid()) return false;
    
    ifs.read(reinterpret_cast<char*>(buffer.get()), static_cast<std::streamsize>(expected));
    if (ifs.gcount() != static_cast<std::streamsize>(expected)) {
        return false;
    }
    
    // 设置图像数据 - 参考main.cpp CPU模式逻辑 (第642-645行)
    image.imgData.nFormat = format;
    image.imgData.nWidth = static_cast<BSIZE>(width);
    image.imgData.nHeight = static_cast<BSIZE>(height);
    image.imgData.pPlanePtr[0] = buffer.get();  // 参考main.cpp第642行: pPlanePtr[0] = yuvData
    image.imgData.pPlanePtr[1] = buffer.get() + static_cast<size_t>(stride) * static_cast<size_t>(height);  // 参考main.cpp第643行
    image.imgData.pPlanePtr[2] = nullptr;
    image.imgData.pPlanePtr[3] = nullptr;
    image.imgData.nRowPitch[0] = static_cast<BSIZE>(stride);  // 参考main.cpp第644行: nRowPitch[0] = nStride
    image.imgData.nRowPitch[1] = static_cast<BSIZE>(stride);  // 参考main.cpp第645行: nRowPitch[1] = nStride
    image.imgData.nRowPitch[2] = 0;
    image.imgData.nRowPitch[3] = 0;
    image.imgData.nTextureID = 0;
    
    return true;
}

// 配置结构体
struct Config {
    std::string imgdir;
    std::string cfgdir;
    std::string modeldir;
    int selectMode = 0;
    int nCameraPos = 0;
    int nWidth = 640;
    int nHeight = 640;
    int nStride = 640;
    float zoom = 1.0f;
};

// 解析命令行参数
static bool parse_arguments(int argc, char** argv, Config& config) {
    auto cli = (
        value("runMode", config.selectMode).doc("1:Preview mode(GPU),2:Offline mode(CPU)"),
        required("-ImageDir") & opt_value("Input image dir", config.imgdir),
        required("-Config") & opt_value("Input cfg file", config.cfgdir),
        required("-model") & opt_value("Model file", config.modeldir),
        option("-CameraPos") & value("Rotation", config.nCameraPos),
        option("-zoom") & value("Image zoom", config.zoom),
        option("-inWidth") & value("Image width", config.nWidth),
        option("-inHeight") & value("Image height", config.nHeight),
        option("-inStride") & value("Image stride", config.nStride)
    );

    if (!parse(argc, argv, cli)) {
        std::printf("Parse error!\n");
        std::cout << make_man_page(cli, argv[0]);
        return false;
    }
    
    std::cout << make_man_page(cli, argv[0]);
    return true;
}

// 初始化AI场景引擎
static BHANDLE initialize_aiscene(const Config& config) {
    BSInitCfg initCfg{};
    
    // 根据selectMode设置模式 - 参考main.cpp的逻辑
    if (config.selectMode == 1) {
        initCfg.eMode = BST_AISCENE_MODE_LIVE;
        initCfg.ProcessType = BST_AISCENE_Type_GPU;
    } else {
        initCfg.eMode = BST_AISCENE_MODE_SIMULATE;
        initCfg.ProcessType = BST_AISCENE_Type_CPU;
    }
    
    initCfg.config.eConfigType = BST_CFG_TYPE_FILE;
    initCfg.config.cfg.pConfigFile = config.cfgdir.c_str();
    
    if (!config.modeldir.empty()) {
        initCfg.tfbuffer.pBuffer = const_cast<char*>(config.modeldir.c_str());
        initCfg.tfbuffer.nLen = config.modeldir.length();
    } else {
        initCfg.tfbuffer.pBuffer = nullptr;
        initCfg.tfbuffer.nLen = 0;
    }
    
    initCfg.memory.mm_malloc = nullptr;
    initCfg.memory.mm_free = nullptr;

    BHANDLE handle = -1;
    BRESULT result = BSTAISceneInitV2(&initCfg, &handle);
    if (result != BST_AISCENE_SUCCESS) {
        std::cerr << "BSTAISceneInitV2 failed: " << result << std::endl;
        return -1;
    }
    
    return handle;
}

// 获取场景映射
static std::map<int, std::string> get_scene_map(BHANDLE handle) {
    std::map<int, std::string> mapScene;
    int sceneId[MAX_SCNENE_NUM];
    char strscene[MAX_SCNENE_NUM][16];
    int sceneNum = 0;

    if (BATAISceneGetSceneMap(handle, sceneId, strscene, &sceneNum) == BST_AISCENE_SUCCESS) {
        for (int i = 0; i < sceneNum; ++i) {
            mapScene[sceneId[i]] = std::string(strscene[i]);
        }
    }

    return mapScene;
}

// 处理单个图像文件
static bool process_image(const std::string& filepath, BHANDLE handle, const Config& config,
                         const std::map<int, std::string>& sceneMap, const std::string& outDir) {
    BSTImage image{};
    ImageBuffer buffer(0);

    // 获取文件扩展名
    std::string ext = filepath.substr(filepath.find_last_of('.'));
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    bool loaded = false;

    // 根据文件类型加载图像
    if (ext == ".nv21" || ext == ".nv12") {
        if (config.nWidth > 0 && config.nHeight > 0) {
            int stride = (config.nStride > 0) ? config.nStride : config.nWidth;
            BSTImageType format = (ext == ".nv21") ? BST_IMAGE_TYPE_NV21 : BST_IMAGE_TYPE_NV12;
            loaded = load_yuv_file(filepath, config.nWidth, config.nHeight, stride, format, image, buffer);
        }
    } else {
        loaded = load_image_file(filepath, image, buffer);
    }

    if (!loaded) {
        std::cerr << "  Failed to load image: " << filepath << std::endl;
        return false;
    }

    // 设置元数据
    set_image_metadata(image, config.nCameraPos, config.zoom);

    // 执行推理
    int outNum = 0;
    int aiType[MAX_OUT_NUM] = {0};
    float aiConf[MAX_OUT_NUM] = {0.0f};
    float aiArea[MAX_OUT_NUM] = {0.0f};

    BRESULT result = BSTAISceneRenderV2Ext(handle, &image, &outNum, aiType, aiConf, aiArea);
    if (result != BST_AISCENE_SUCCESS) {
        std::cerr << "  BSTAISceneRenderV2Ext failed: " << result << std::endl;
        return false;
    }

    // 输出结果
    if (outNum <= 0) {
        std::cout << "  Result: none" << std::endl;
    } else {
        for (int i = 0; i < outNum; ++i) {
            const int typeId = aiType[i];
            const float conf = aiConf[i];
            auto it = sceneMap.find(typeId);
            if (it != sceneMap.end()) {
                std::cout << "  id=" << typeId << ", name=" << it->second << ", conf=" << conf << std::endl;
            } else {
                std::cout << "  id=" << typeId << ", conf=" << conf << std::endl;
            }
        }
    }

#ifdef AISCENE_USE_OPENCV
    // 如果启用OpenCV，保存带注释的图像
    if (ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".bmp") {
        cv::Mat vis = cv::imread(filepath, cv::IMREAD_COLOR);
        if (!vis.empty()) {
            int y = 28;
            const int lineStep = 26;
            for (int i = 0; i < outNum; ++i) {
                const int typeId = aiType[i];
                const float conf = aiConf[i];
                auto it = sceneMap.find(typeId);

                std::ostringstream oss;
                if (it != sceneMap.end()) {
                    oss << it->second << " (" << typeId << ")";
                } else {
                    oss << "id=" << typeId;
                }
                oss << std::fixed << std::setprecision(3) << ", conf=" << conf;

                cv::putText(vis, oss.str(), cv::Point(10, y), cv::FONT_HERSHEY_SIMPLEX,
                           0.7, cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
                y += lineStep;
            }

            std::string filename = filepath.substr(filepath.find_last_of('/') + 1);
            const std::string outPath = outDir + "/" + filename;
            cv::imwrite(outPath, vis);
        }
    }
#endif

    return true;
}

// 主推理函数
static int run_inference(const Config& config) {
    // 初始化AI场景引擎
    BHANDLE handle = initialize_aiscene(config);
    if (handle == -1) {
        return 3;
    }

    // 获取场景映射
    auto sceneMap = get_scene_map(handle);

    // 获取图像文件列表
    auto files = list_image_files(config.imgdir);
    if (files.empty()) {
        std::cout << "No image files found in directory: " << config.imgdir << std::endl;
        CHECK_ERROR(BSTAISceneUninitV2(handle));
        return 1;
    }

    // 创建输出目录
    const std::string outDir = "infer_out";
    mkdir(outDir.c_str(), 0755);

    // 处理每个图像文件
    std::size_t failed = 0;
    for (const auto& filepath : files) {
        static int fileCount = 0;
        std::cout << "[" << ++fileCount << "/" << files.size() << "] " << filepath << std::endl;

        if (!process_image(filepath, handle, config, sceneMap, outDir)) {
            ++failed;
        }
    }

    // 清理资源
    CHECK_ERROR(BSTAISceneUninitV2(handle));

    std::cout << "Total: " << files.size() << ", Failed: " << failed << std::endl;
    return (failed == 0) ? 0 : 2;
}

int main(int argc, char** argv) {
    std::printf("Build time: %s %s\n", __DATE__, __TIME__);

    Config config;
    if (!parse_arguments(argc, argv, config)) {
        return 1;
    }

    return run_inference(config);
}
