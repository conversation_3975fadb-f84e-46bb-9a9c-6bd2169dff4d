#include "clipp.h"
using clipp::group;
using clipp::value;
using clipp::option;
using clipp::required;
using clipp::opt_value;
#include <iostream>
#include <string>
#include <vector>
#include <cctype>
#include <cstdlib>
#include <algorithm>
#include <fstream>
#include <cstdio>
#include <sstream>
#include <map>
#include <dirent.h>
#include <sys/stat.h>
#include <cstring>
#include "../AiScene/BSTAIScene.h"
#ifdef AISCENE_USE_OPENCV
#include <opencv2/imgproc.hpp>
#include <opencv2/imgcodecs.hpp>
#endif
extern int BSTAISceneRenderV2Ext(BHANDLE handle, BSTImage* mainImg, int* OutNum, int AIType[MAX_OUT_NUM], float AIConf[MAX_OUT_NUM], float Area[MAX_OUT_NUM]);

static void print_help(const char* argv0) {
    std::cout << "Usage:\n";
    std::cout << "  " << argv0 << " <runMode> -ImageDir <dir> -Config <cfg> -model <model> [options]\n";
    std::cout << "  runMode: 1=Preview mode(GPU), 2=Offline mode(CPU)\n";
}

#define checkErrorDemo(x) do { \
    int X__ = (x); \
    if (X__ != 0) std::printf("------------------error ! code = %d ---------------------\n", X__); \
} while(0)

static std::vector<std::string> list_image_files(const std::string& directory) {
    std::vector<std::string> results;
    
    DIR* dir = opendir(directory.c_str());
    if (!dir) {
        return results;
    }
    
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        if (entry->d_type == DT_REG) { // 普通文件
            std::string filename = entry->d_name;
            std::string ext = filename.substr(filename.find_last_of('.'));
            std::transform(ext.begin(), ext.end(), ext.begin(), [](unsigned char c){ return static_cast<char>(std::tolower(c)); });
            
            if (ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".bmp") {
                results.emplace_back(directory + "/" + filename);
            }
        }
    }
    
    closedir(dir);
    return results;
}

static bool load_bstimage_from_file(const std::string& file_path, BSTImage& image, unsigned char*& allocated) {
    int w = 0, h = 0, c = 0;
    allocated = BSTLoadImage(file_path.c_str(), &w, &h, &c);
    if (!allocated || w <= 0 || h <= 0) {
        if (allocated) std::free(allocated);
        allocated = nullptr;
        return false;
    }
    
    // 参考main.cpp中的赋值逻辑
    if (c == 3) {
        // RGB to BGR conversion (like main.cpp中的swap_rgb)
        for (int i = 0; i < w * h; ++i) {
            unsigned char* p = allocated + i * 3;
            std::swap(p[0], p[2]);
        }
        image.imgData.nFormat = BST_IMAGE_TYPE_BGR;
    } else if (c == 1) {
        image.imgData.nFormat = BST_IMAGE_TYPE_GRAY;
    } else {
        std::free(allocated);
        allocated = nullptr;
        return false;
    }
    
    // 参考main.cpp中的imgData赋值
    image.imgData.nWidth = static_cast<BSIZE>(w);
    image.imgData.nHeight = static_cast<BSIZE>(h);
    image.imgData.pPlanePtr[0] = allocated;
    image.imgData.pPlanePtr[1] = nullptr;
    image.imgData.pPlanePtr[2] = nullptr;
    image.imgData.pPlanePtr[3] = nullptr;
    image.imgData.nRowPitch[0] = static_cast<BSIZE>(w);
    image.imgData.nRowPitch[1] = 0;
    image.imgData.nRowPitch[2] = 0;
    image.imgData.nRowPitch[3] = 0;
    image.imgData.nTextureID = 0;

    // 参考main.cpp中的metaData赋值
    image.metaData.nRotation = BST_ROTATION_0;
    image.metaData.nFace.m_FaceNum = 0;
    image.metaData.nDacVal = 0;  // focal distance
    image.metaData.nGain = 1.0f;
    image.metaData.nExposure = 0;
    image.metaData.nISO = 0;
    image.metaData.nGyro[0] = 0.0f;
    image.metaData.nGyro[1] = 0.0f;
    image.metaData.nGyro[2] = 0.0f;
    image.metaData.nZoom = 1.0f;
    return true;
}

static bool load_bstimage_from_yuv420sp(const std::string& file_path,
                                        int width,
                                        int height,
                                        int stride,
                                        BSTImageType format,
                                        BSTImage& image,
                                        unsigned char*& allocated) {
    if (width <= 0 || height <= 0) return false;
    if (stride <= 0) stride = width;
    const size_t expected = static_cast<size_t>(stride) * static_cast<size_t>(height) * 3 / 2;
    std::ifstream ifs(file_path, std::ios::binary);
    if (!ifs) return false;
    allocated = reinterpret_cast<unsigned char*>(std::malloc(expected));
    if (!allocated) return false;
    ifs.read(reinterpret_cast<char*>(allocated), static_cast<std::streamsize>(expected));
    if (ifs.gcount() != static_cast<std::streamsize>(expected)) {
        std::free(allocated);
        allocated = nullptr;
        return false;
    }
    image.imgData.nFormat = format; // BST_IMAGE_TYPE_NV21 or BST_IMAGE_TYPE_NV12
    image.imgData.nWidth  = static_cast<BSIZE>(width);
    image.imgData.nHeight = static_cast<BSIZE>(height);
    image.imgData.pPlanePtr[0] = allocated;
    image.imgData.pPlanePtr[1] = allocated + static_cast<size_t>(stride) * static_cast<size_t>(height);
    image.imgData.pPlanePtr[2] = nullptr;
    image.imgData.pPlanePtr[3] = nullptr;
    image.imgData.nRowPitch[0] = static_cast<BSIZE>(stride);
    image.imgData.nRowPitch[1] = static_cast<BSIZE>(stride);
    image.imgData.nRowPitch[2] = 0;
    image.imgData.nRowPitch[3] = 0;
    image.imgData.nTextureID = 0;

    image.metaData.nRotation = BST_ROTATION_0;
    image.metaData.nFace.m_FaceNum = 0;
    image.metaData.nDacVal = 0;
    image.metaData.nGain = 1.0f;
    image.metaData.nExposure = 0;
    image.metaData.nISO = 0;
    image.metaData.nGyro[0] = 0.0f;
    image.metaData.nGyro[1] = 0.0f;
    image.metaData.nGyro[2] = 0.0f;
    image.metaData.nZoom = 1.0f;
    return true;
}

static int run_infer(const std::string& dir, const std::string& cfg, const std::string& modeldir,
                     int selectMode, int nCameraPos, float zoom, int nWidth, int nHeight, int nStride) {
    // Variables aligned with main.cpp defaults

    BSInitCfg initCfg{};
    // Set mode based on selectMode (aligned with main.cpp)
    if (selectMode == 1) {
        initCfg.eMode = BST_AISCENE_MODE_LIVE;
        initCfg.ProcessType = BST_AISCENE_Type_GPU;
    } else {
        initCfg.eMode = BST_AISCENE_MODE_SIMULATE;
        initCfg.ProcessType = BST_AISCENE_Type_CPU;
    }
    initCfg.config.eConfigType = BST_CFG_TYPE_FILE;
    initCfg.config.cfg.pConfigFile = cfg.c_str();
    // Set model path if provided (aligned with main.cpp)
    if (!modeldir.empty()) {
        initCfg.tfbuffer.pBuffer = const_cast<char*>(modeldir.c_str());
        initCfg.tfbuffer.nLen = modeldir.length();
    } else {
        initCfg.tfbuffer.pBuffer = nullptr;
        initCfg.tfbuffer.nLen = 0;
    }
    initCfg.memory.mm_malloc = nullptr;
    initCfg.memory.mm_free = nullptr;

    BHANDLE handle = -1;
    BRESULT r = BSTAISceneInitV2(&initCfg, &handle);
    if (r != BST_AISCENE_SUCCESS) {
        std::cerr << "BSTAISceneInitV2 failed: " << r << std::endl;
        return 3;
    }

    // Retrieve scene map (id -> name)
    int sceneId[MAX_SCNENE_NUM];
    char strscene[MAX_SCNENE_NUM][16];
    int sceneNum = 0;
    if (BATAISceneGetSceneMap(handle, sceneId, strscene, &sceneNum) != BST_AISCENE_SUCCESS) {
        sceneNum = 0; // fallback: no names
    }
    std::map<int, std::string> mapScene;
    for (int i = 0; i < sceneNum; ++i) {
        mapScene[sceneId[i]] = std::string(strscene[i]);
    }

    auto files = list_image_files(dir);
    // Prepare output directory for annotated results
    const std::string outDir = "infer_out";
    // 使用mkdir创建输出目录
    mkdir(outDir.c_str(), 0755);
    std::size_t failed = 0;
    for (const auto& f : files) {
        static int fileCount = 0;
        std::cout << "[" << ++fileCount << "/" << files.size() << "] " << f << '\n';
        BSTImage image{};
        unsigned char* allocated = nullptr;
        // SelectMode=2: support BGR images and NV21/NV12 raw frames
        std::string ext = f.substr(f.find_last_of('.'));
        std::transform(ext.begin(), ext.end(), ext.begin(), [](unsigned char c){ return static_cast<char>(std::tolower(c)); });
        bool ok = false;
        if (ext == ".nv21" || ext == ".nv12") {
            // Use parameters from command line (aligned with main.cpp)
            if (nWidth > 0 && nHeight > 0) {
                int stride = (nStride > 0) ? nStride : nWidth;
                ok = load_bstimage_from_yuv420sp(
                    f,
                    nWidth,
                    nHeight,
                    stride,
                    (ext == ".nv21") ? BST_IMAGE_TYPE_NV21 : BST_IMAGE_TYPE_NV12,
                    image,
                    allocated
                );
            }
        } else {
            ok = load_bstimage_from_file(f, image, allocated);
        }
        if (!ok) {
            std::cerr << "  load image failed" << std::endl;
            failed += 1;
            continue;
        }

        // Set metadata from variables (like main.cpp)
        image.metaData.nRotation = (BSTRotation)nCameraPos;
        image.metaData.nZoom = zoom;
        // 参考main.cpp中的其他metaData赋值
        image.metaData.nDacVal = 0;  // focal distance
        image.metaData.nFace.m_FaceNum = 0;
        image.metaData.nGain = 1.0f;
        image.metaData.nExposure = 0;
        image.metaData.nISO = 0;
        image.metaData.nGyro[0] = 0.0f;
        image.metaData.nGyro[1] = 0.0f;
        image.metaData.nGyro[2] = 0.0f;

        int outNum = 0;
        int aiType[MAX_OUT_NUM] = {0};
        float aiConf[MAX_OUT_NUM] = {0.0f};
        float aiArea[MAX_OUT_NUM] = {0.0f};
        BRESULT rr = BSTAISceneRenderV2Ext(handle, &image, &outNum, aiType, aiConf, aiArea);
        if (rr != BST_AISCENE_SUCCESS) {
            std::cerr << "  BSTAISceneRenderV2Ext failed: " << rr << std::endl;
            failed += 1;
            std::free(allocated);
            continue;
        }
        if (outNum <= 0) {
            std::cout << "  result: none" << std::endl;
        } else {
            for (int i = 0; i < outNum; ++i) {
                const int typeId = aiType[i];
                const float conf = aiConf[i];
                auto it = mapScene.find(typeId);
                if (it != mapScene.end()) std::cout << "  id=" << typeId << ", name=" << it->second << ", conf=" << conf << std::endl;
                else                      std::cout << "  id=" << typeId << ", conf=" << conf << std::endl;
            }
        }

#ifdef AISCENE_USE_OPENCV
        // If this is a standard image file, draw text and save using OpenCV
        std::string ext_for_save = f.substr(f.find_last_of('.'));
        std::transform(ext_for_save.begin(), ext_for_save.end(), ext_for_save.begin(), [](unsigned char c){ return static_cast<char>(std::tolower(c)); });
        if (ext_for_save == ".jpg" || ext_for_save == ".jpeg" || ext_for_save == ".png" || ext_for_save == ".bmp") {
            cv::Mat vis = cv::imread(f, cv::IMREAD_COLOR);
            if (!vis.empty()) {
                int y = 28;
                const int lineStep = 26;
                for (int i = 0; i < outNum; ++i) {
                    const int typeId = aiType[i];
                    const float conf = aiConf[i];
                    auto it = mapScene.find(typeId);
                    std::ostringstream oss;
                    if (it != mapScene.end()) oss << it->second << " (" << typeId << ")"; else oss << "id=" << typeId;
                    oss.setf(std::ios::fixed); oss.precision(3);
                    oss << ", conf=" << conf;
                    cv::putText(vis, oss.str(), cv::Point(10, y), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
                    y += lineStep;
                }
                // 使用传统的路径拼接方式
                std::string filename = f.substr(f.find_last_of('/') + 1);
                const std::string outPath = outDir + "/" + filename;
                cv::imwrite(outPath, vis);
            }
        }
#endif
        std::free(allocated);
    }

    checkErrorDemo(BSTAISceneUninitV2(handle));
    std::cout << "Total: " << files.size() << ", Failed: " << failed << std::endl;
    return 0;
}

int main(int argc, char** argv) {
    printf("build time: %s %s\n", __DATE__, __TIME__);

    // Variables aligned with main.cpp
    std::string imgdir, cfgdir, modeldir;
    int selectMode = 0;
    int nCameraPos = 0;
    int nWidth = 640;
    int nHeight = 640;
    int nStride = 640;
    float zoom = 1.0f;

    // Command line interface aligned with main.cpp
    auto cli = (
        value("runMode", selectMode).doc("1:Preview mode(GPU),2:Offline mode(CPU)"),
        required("-ImageDir") & opt_value("Input image dir", imgdir),
        required("-Config") & opt_value("Input cfg file", cfgdir),
        required("-model") & opt_value("Model file", modeldir),
        option("-CameraPos") & value("Rotation", nCameraPos),
        option("-zoom") & value("Image zoom", zoom),
        option("-inWidth") & value("Image width", nWidth),
        option("-inHeight") & value("Image height", nHeight),
        option("-inStride") & value("Image stride", nStride)
    );

    if (!parse(argc, argv, cli)) {
        printf("parse error !! \n");
        std::cout << make_man_page(cli, argv[0]);
        return 1;
    } else {
        std::cout << make_man_page(cli, argv[0]);
    }

    return run_infer(imgdir, cfgdir, modeldir, selectMode, nCameraPos, zoom, nWidth, nHeight, nStride);
}


