#include "clipp.h"
//#include "AlgPipeLine.hpp"
//using namespace clipp;
using clipp::group;
using clipp::value;
using clipp::option;
using clipp::required;
using clipp::opt_value;
#include "BSTAIScene.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include <string>
#include <string.h>
#if !defined(WIN32)
#include <unistd.h>
#define Sleep(MS) usleep((MS)*1000)
#else
#include <time.h>
#include <windows.h>
//#include"vld/include/vld.h"
#endif
#include <iostream>
#include <vector>
#ifndef STB_IMAGE_WRITE_IMPLEMENTATION
#define STB_IMAGE_WRITE_IMPLEMENTATION
#endif
#include "stb_image_write.h"
extern int crateGraphic(char* path);
using std::string;
using std::to_string;
using std::vector;
using std::cout;
using std::endl;
bool isTestDir = false;
char TestPath[256] = { 0 };
int histogram(unsigned char* data, int nx, int ny, unsigned long* histogram);
#ifndef _WIN32
#include <dlfcn.h>
#endif
#include "../AiScene/BSTAIScene.h"
#ifndef WIN32
#include <unistd.h>
#define Sleep(MS) usleep((MS)*1000)
#include <dirent.h>

#else
#include <time.h>
#include <windows.h>
//#include"vld/include/vld.h"
#include <iostream>
#include <cstring>        // for strcat()
#include <io.h>
#endif

#define checkErrorDemo(x) \
{\
int X = (x);\
if(X!=0) printf("------------------error ! code = %d ---------------------\n",X);\
}

extern void OGL_Test_init();
extern void OGL_getTextues(unsigned int& TexId, int w, int h, unsigned char* inputPtr);
extern void OGL_deleteTextues(unsigned int TexId);
extern void OGL_Test_Finish();
extern unsigned char* load_bmp_yuv420(char* name, int* w, int* h, int* c, int comp, int order);
extern unsigned char* load_bmp(char* name, int* w, int* h, int* c, int comp);
extern void bgr_2_yuv420(unsigned char* bgr, unsigned char* yuv, int w, int h, int c, int order);
extern int BSTAISceneRenderV2Ext(BHANDLE handle, BSTImage* mainImg, int* OutNum, int AIType[MAX_OUT_NUM], float AIConf[MAX_OUT_NUM], float Area[MAX_OUT_NUM]);
string cutStr(string str, string cut)
{
	string baseName = str;
	for (int i = 0; i < 128; i++)
	{
		int tagPos = baseName.find_last_of(cut);
		if (tagPos == string::npos)
		{
			break;
		}
		baseName.erase(tagPos, cut.length());
	}
	return baseName;
}

void NV21_T_RGB(unsigned int width, unsigned int height, unsigned char* yuyv, unsigned char* rgbData)
{
	const int nv_start = width * height;
	unsigned char* rgb = new unsigned char[width * height * 3];
	uint32_t i, j, index = 0, rgb_index = 0;
	uint8_t y, u, v;
	int r, g, b, nv_index = 0;

	for (i = 0; i < height; i++)
	{
		for (j = 0; j < width; j++)
		{
			nv_index = i / 2 * width + j - j % 2;

			y = yuyv[rgb_index];
			v = yuyv[nv_start + nv_index];
			u = yuyv[nv_start + nv_index + 1];

			r = y + (140 * (v - 128)) / 100;						 //r
			g = y - (34 * (u - 128)) / 100 - (71 * (v - 128)) / 100; //g
			b = y + (177 * (u - 128)) / 100;						 //b

			if (r > 255)
				r = 255;
			if (g > 255)
				g = 255;
			if (b > 255)
				b = 255;
			if (r < 0)
				r = 0;
			if (g < 0)
				g = 0;
			if (b < 0)
				b = 0;

			index = rgb_index % width + (height - i - 1) * width;

			rgb[i * width * 3 + 3 * j + 2] = b;
			rgb[i * width * 3 + 3 * j + 1] = g;
			rgb[i * width * 3 + 3 * j + 0] = r;

			rgb_index++;
		}
	}
	memcpy(rgbData, rgb, width * height * 3);
	delete[] rgb;
}

#if defined(WIN32)
int getFiles(char* findDir, vector<string>& vecFindName, char* tag = nullptr)
{
	char dir[200] = { 0 };
	strcat(dir, findDir);
	strcat(dir, "/*");
	intptr_t handle;
	_finddata_t findData;
	if (tag != nullptr)
	{
		strcat(dir, tag);
	}
	handle = _findfirst(dir, &findData);    // ����Ŀ¼�еĵ�һ���ļ�
	if (handle == -1)
	{
		cout << "Failed to find first file!\n";
		return 0;
	}
	do
	{
		if (findData.attrib & _A_SUBDIR
			&& strcmp(findData.name, ".") == 0
			&& strcmp(findData.name, "..") == 0
			)    // �Ƿ�����Ŀ¼���Ҳ�Ϊ"."��".."
			cout << findData.name << "\t<dir>\n";
		else
		{
			cout << findData.name << "\t" << findData.size << endl;
			vecFindName.push_back(string(findData.name));
		}
	} while (_findnext(handle, &findData) == 0);    // ����Ŀ¼�е���һ���ļ�

	cout << "Done!\n";
	_findclose(handle);    // �ر��������
	return vecFindName.size();
}
#else
int getFiles(const string path, vector<string>& files, string tag) {

	int iFileCnt = 0;
	DIR* dirptr = NULL;
	struct dirent* dirp;
	printf("path : %s \n", path.c_str());
	if ((dirptr = opendir(path.c_str())) == NULL)//��һ��Ŀ¼
	{
		printf("open dir %s failed \n", path.c_str());
		return 0;
	}

	while ((dirp = readdir(dirptr)) != NULL)
	{
		if (strstr(dirp->d_name, tag.c_str()))//�ж��Ƿ�Ϊ�ļ��Լ��ļ���׺��
		{
			files.push_back(dirp->d_name);
			printf("file:%s\n", dirp->d_name);
		}
		iFileCnt++;
	}
	closedir(dirptr);

	return iFileCnt;
}
#endif

void swap_rgb(unsigned char* data, int w, int h)
{
	for (int i = 0; i < w * h * 3; i += 3)
	{
		unsigned char tmp = data[i];
		data[i] = data[i + 2];
		data[i + 2] = tmp;
	}
}
#define SIM 1
#include <map>
void load_yuv(char* name, int w, int h, int stride, unsigned char* data)
{
	FILE* fp = fopen(name, "rb");
	fread(data, 1, w * h * 3 / 2, fp);
	fclose(fp);
}
#if !defined(WIN32)
vector<string> g_vec_rgbtag{ ".jpg",".JPG",".jpeg",".JPEG",".bmp","BMP",".png",".PNG" };
vector<string> g_vec_yuvtag{ ".NV21",".nv21",".nv12",".NV12" };
#else
vector<string> g_vec_rgbtag{ ".jpg",".jpeg",".bmp",".png" };
vector<string> g_vec_yuvtag{ ".nv21",".nv12" };
#endif

struct InitParam
{
	BSInitCfg conf;
	BHANDLE handle=-1;
	int ret=0;
};

struct ProcessParam
{
	BHANDLE handle;
	BSTImage mainImg;
	int OutNum;
	int AIType[MAX_OUT_NUM];
	float AIConf[MAX_OUT_NUM];
	float Area[MAX_OUT_NUM];
	int ret = 0;
};

struct UninitParam
{
	BHANDLE handle;
	int ret = 0;
};

int BSTAISceneRenderV2ExtCallBack(ProcessParam& processParam, void*userData)
{
	int* data = (int*)userData;
	processParam.handle = data[0];
	int ret = BSTAISceneRenderV2(processParam.handle, &processParam.mainImg, &processParam.OutNum, processParam.AIType, processParam.AIConf);
	processParam.ret = ret;
	return 0;

}

int BSTAISceneInitV2CallBack(InitParam& initParam, void* &userData)
{
	int ret = BSTAISceneInitV2(&initParam.conf, &initParam.handle);
	initParam.ret = ret;
	int* data = (int*)userData;
	data[0] = initParam.handle;
	return 0;
}

int BSTAISceneUninitV2CallBack(UninitParam& uninitParam, void* userData)
{
	int* data = (int*)userData;
	uninitParam.handle = data[0];
	int ret = BSTAISceneUninitV2(uninitParam.handle);
	uninitParam.ret = ret;
	return 0;
}

int main(int argc, char* argv[])
{
	printf("build time: %s %s\n", __DATE__, __TIME__);
	//ALGPipeLine<InitParam, ProcessParam, UninitParam> algPipeLine(&BSTAISceneInitV2CallBack, &BSTAISceneRenderV2ExtCallBack, &BSTAISceneUninitV2CallBack);
	//algPipeLine.getRunnerPolicy("./poxy.json");
	//InitParam initParam; UninitParam uninitParam; ProcessParam processParam;
	//initParam.conf.config.cfg.pConfigFile = "./control_param_AiSceneV2_v2.cfg";
	//initParam.conf.config.eConfigType = BST_CFG_TYPE_FILE;
	//initParam.conf.eMode = BST_AISCENE_MODE_SIMULATE;
	//initParam.conf.ProcessType = BST_AISCENE_Type_CPU;
	//initParam.conf.tfbuffer.pBuffer = "./models";
	//initParam.conf.tfbuffer.nLen = strlen("./models");
	//initParam.handle = -1;
	//int algHandle = -1;
	//algPipeLine.runPipeLine(&initParam, &processParam, &uninitParam,(void*)&algHandle);
	int i, j;
	char imagePath[256], imagename[256], ext[5], filename[256];
	char configFile[128] = { 0 };
	int selectMode = 0;
	int mode = BST_AISCENE_MODE_SIMULATE;
	int nCameraPos = 0;
	string cameraPos;
	int nWidth;
	int nHeight;
	int nStride;
	int scene = 100;
	float zoom = 1.0f;
	int AIType = 100;
	char modelPath[128];
	string imgdir, cfgdir, modeldir;
	memset(modelPath, 0, 128);
	auto cli = (
		value("runMode", selectMode).doc("1:Preview mode(GPU),2:Offline mode(CPU)"),
		required("-ImageDir") & opt_value("Input image dir", imgdir),//.set(imgdir).doc("input image dir"),
		required("-Config") & opt_value("Input cfg file", cfgdir),// .set(cfgdir).doc("input cfg file"),
		required("-model") & opt_value("Model file", modeldir),//.set(modeldir).doc("model file "),
		option("-CameraPos") & value("Rotation", nCameraPos),// .set(cameraPos).doc("Rotation"),
		option("-zoom") & value("Image zoom", zoom),//set(zoom).doc("Image zoom"),
		option("-inWidth") & value("Image width (nv21 or nv21)", nWidth),//.set(nWidth).doc("Image width (nv21 or nv21)"),
		option("-inHeight") & value("Image height (nv21 or nv21)", nHeight),//.set(nHeight).doc("Image height (nv21 or nv21)"),
		option("-inStride") & value("Image stride (nv21 or nv21)", nStride)//.set(nStride).doc("Image stride (nv21 or nv21)")
		);
	if (!parse(argc, argv, cli))
	{
		printf("parse error !! \n");
		cout << make_man_page(cli, argv[0]);
	}
	else
	{
		cout << make_man_page(cli, argv[0]);
	}
	strcpy(configFile, cfgdir.c_str());
	strcpy(imagePath, imgdir.c_str());
	strcpy(modelPath, modeldir.c_str());
	printf("AIScene Config Path = %s\n", configFile);
	BHANDLE     instance1 = -1, instance2 = -1;
	BSInitCfg   initCfg2;
	BRESULT     res, res2;
	std::string str0, str1;
	mode = BST_AISCENE_MODE_LIVE;
	// cutStr(pConfigAIScenePath,str0,str1);
	initCfg2.eMode = BST_AISCENE_MODE_SIMULATE;
	initCfg2.ProcessType = BST_AISCENE_Type_CPU;
	initCfg2.config.eConfigType = BST_CFG_TYPE_FILE;
	initCfg2.config.cfg.pConfigFile = configFile;
	if (modelPath[0] != '\0')
	{
		initCfg2.tfbuffer.pBuffer = modelPath;//BSTAI  mbv2_dl3_reduced
		initCfg2.tfbuffer.nLen = strlen((const char*)modelPath);
	}
	else
	{
		initCfg2.tfbuffer.pBuffer = nullptr;
		initCfg2.tfbuffer.nLen = 0;
	}
	vector<string> fileImages;
	int cntBmp = 0; ;
	int cntYUV = 0;
	//OGL_Test_init();

	if (selectMode == 1)
	{
		for (int i = 0; i < g_vec_rgbtag.size(); i++)
		{
			cntYUV += getFiles(imagePath, fileImages, (char*)g_vec_rgbtag[i].c_str());
		}
		for (int i = 0; i < g_vec_yuvtag.size(); i++)
		{
			cntYUV += getFiles(imagePath, fileImages, (char*)g_vec_yuvtag[i].c_str());
		}
		initCfg2.eMode = BST_AISCENE_MODE_LIVE;
		initCfg2.ProcessType = BST_AISCENE_Type_GPU;
		printf("live mode \n");
	}
	else if (selectMode == 2)
	{
		for (int i = 0; i < g_vec_rgbtag.size(); i++)
		{
			cntYUV += getFiles(imagePath, fileImages, (char*)g_vec_rgbtag[i].c_str());
		}
		for (int i = 0; i < g_vec_yuvtag.size(); i++)
		{
			cntYUV += getFiles(imagePath, fileImages, (char*)g_vec_yuvtag[i].c_str());
		}
		initCfg2.eMode = BST_AISCENE_MODE_SIMULATE;//offine
		initCfg2.ProcessType = BST_AISCENE_Type_CPU;

		printf("sim mode \n");
	}
	else
	{
		for (int i = 0; i < g_vec_rgbtag.size(); i++)
		{
			cntYUV += getFiles(imagePath, fileImages, (char*)g_vec_rgbtag[i].c_str());
		}
		for (int i = 0; i < g_vec_yuvtag.size(); i++)
		{
			cntYUV += getFiles(imagePath, fileImages, (char*)g_vec_yuvtag[i].c_str());
		}
		initCfg2.eMode = BST_AISCENE_MODE_CAPTURE;//cap
		initCfg2.ProcessType = BST_AISCENE_Type_CPU;
		printf("------ cap mode not support !!!!!! -----\n");
		printf("------ cap mode not support !!!!!! -----\n");
		printf("------ cap mode not support !!!!!! -----\n");
		printf("------ cap mode not support !!!!!! -----\n");
	}
	printf("\nBSTAISceneInit...\n");
	unsigned int InTexId = 0, OutTexId = 0;
	BSTImage DSLRImg;
	BSTImage DSLRImgCap, DSLRImgCapRst;
	//#endif
	//#if 0
	for (int x = 0; x < 1; x++)
	{
		res = BSTAISceneInitV2(&initCfg2, &instance2);
		//res = BSTAISceneUninitV2(instance2);
		//res = BSTAISceneInitV2(&initCfg2, &instance2);
		checkErrorDemo(res);
		std::map<int, string> mapScene;//ӳ���
		//char c_strMap[MAX_SCNENE_NUM][16];
		//for (int i = 0; i < MAX_SCNENE_NUM; i++)
		//{
		//	memset(c_strMap[0], 0, 16);
		//}
		int sceneId[MAX_SCNENE_NUM];
		char strscene[MAX_SCNENE_NUM][16];
		int sceneNum = 0;
		BATAISceneGetSceneMap(instance2, sceneId, strscene, &sceneNum);//��ȡ��ǩֵ(int) �� ������ǩ(string) ӳ���map<int ,string>
		for (int i = 0; i < sceneNum; i++)
		{
			mapScene[sceneId[i]] = string(strscene[i]);
		//	strcpy(c_strMap[sceneId[i]], strscene[i]);
		}

		//for (int i = 0; i < sceneNum; i++)
		//{
		//	printf("%d : %s", sceneId[i], c_strMap[sceneId[i]]);
		//}
		if (selectMode == 1)//for gpu live
			Sleep(8000);
		printf("distance:%d\n", instance2);
		string txtName = "./scene.txt";
		string txtName2 = "./scene_string.txt";
		FILE* fpRst = fopen(txtName.c_str(), "w");
		FILE* fpRst2 = fopen(txtName2.c_str(), "w");
		FILE* fp_other = fopen("noclass.txt", "w");
		if (BST_AISCENE_SUCCESS != res)
		{
			printf("BSTAISceneInit Fails\n");
			printf("Error Code: %d\n", res);
			return -1;
		}
		int w, h, c;
		// fileImages.size()
		vector<string> seceneVec[42];
		bool isArray = false;
		for (int n = 0; n < fileImages.size(); n++)
		{
			BSTImage mainImage;
			// mainImage.img_name = fileImages[n];
			memset(imagename, 0, sizeof(imagename));
			printf("read file %d/%d %s/%s \n", n, fileImages.size(), imagePath, fileImages[n].c_str());
			sprintf(imagename, "%s/%s", imagePath, fileImages[n].c_str());
			
			// string::size_type tmp_idx;
			// tmp_idx = fileImages[n].find("face");
			// if (tmp_idx != string::npos){
			// 	continue;
			// }
			// tmp_idx = fileImages[n].find("baby");
			// if (tmp_idx != string::npos){
			// 	continue;
			// }
			// tmp_idx = fileImages[n].find("backlight");
			// if (tmp_idx != string::npos){
			// 	continue;
			// }
			// tmp_idx = fileImages[n].find("person");
			// if (tmp_idx != string::npos){
			// 	continue;
			// }
			// tmp_idx = fileImages[n].find("people");
			// if (tmp_idx != string::npos){
			// 	continue;
			// }

			int OutNum = 0;
			int AIType[MAX_OUT_NUM];
			float AIConf[MAX_OUT_NUM];
			float AIArea[MAX_OUT_NUM];
			if (selectMode == 1)//for gpu live
			{
				//mainImage.imgData.nFormat = BST_IMAGE_TYPE_NV12;
				unsigned char* ImgData = nullptr;
				unsigned char* yuvData = nullptr;
				string strName = string(imagename);
				if (strName.find(".bmp") != string::npos || strName.find(".BMP") != string::npos)
				{
					unsigned char* data = BSTLoadImage(imagename, &w, &h, &c);
					ImgData = (unsigned char*)malloc(nWidth * nHeight * 3);
					nWidth = w;
					nHeight = h;
					swap_rgb(data, nWidth, nHeight);//bgr2rgb
					memcpy(ImgData, data, nWidth * nHeight * 3);
					nWidth = w;
					nHeight = h;
					yuvData = ImgData;
					nStride = nWidth;
					free(data);
				}
				else if (strName.find(".jpg") != string::npos || strName.find(".JPG") != string::npos || strName.find(".jpeg") != string::npos
					|| strName.find(".JPEG") != string::npos || strName.find(".PNG") != string::npos || strName.find(".png") != string::npos)
				{
					int ch = 0;
					unsigned char* data = BSTLoadImage(imagename, &nWidth, &nHeight, &ch);
					ImgData = (unsigned char*)malloc(nWidth * nHeight * 3);
					yuvData = ImgData;
					memcpy(ImgData, data, nWidth * nHeight * 3);
					nStride = nWidth;
					free(data);
					isArray = true;
				}

				nStride = nWidth; /* FIXME */
				mainImage.imgData.nWidth = nWidth;
				mainImage.imgData.nHeight = nHeight;
				mainImage.imgData.pPlanePtr[0] = ImgData;//BGRImage.GetImageData();
				mainImage.imgData.pPlanePtr[1] = NULL;
				mainImage.imgData.nRowPitch[0] = nStride;
				mainImage.imgData.nRowPitch[1] = nStride;
				mainImage.metaData.nRotation = (BSTRotation)(nCameraPos);
				mainImage.metaData.nDacVal = 0; //focal distance
				mainImage.metaData.nZoom = zoom;  //image Zoom
				mainImage.metaData.nFace.m_FaceNum = 0;
				OGL_getTextues(InTexId, nWidth, nHeight, ImgData);
				//OGL_getTextues(OutTexId, nWidth, nHeight, nullptr);
				mainImage.imgData.nTextureID = InTexId;
				printf("BSTDSLRRender...\n");
#if defined(SIM)
				res = BSTAISceneRenderV2Ext(instance2, &mainImage, &OutNum, AIType, AIConf, AIArea);
#else
				res = BSTAISceneRenderV2(instance2, &mainImage, &OutNum, AIType, AIConf);
#endif
				printf("\n");
				for (int nu = 0; nu < OutNum; nu++)
				{
					printf("id[%d]:%d,%f\n", nu, AIType[nu], AIConf[nu]);
					printf("sceneStr = %s \n", mapScene[AIType[nu]].c_str());
				}
				printf("\n");
				OGL_deleteTextues(InTexId);
				//OGL_deleteTextues(OutTexId);
				free(ImgData);
				ImgData = nullptr;
				Sleep(200);
			}
			else//for cpu (cap or simulation)
			{
				int eType = 0;
				int order = 0;
				string strName = string(imagename);
				if (strName.find(".nv21") != string::npos || strName.find(".NV21") != string::npos)
				{
					mainImage.imgData.nFormat = BST_IMAGE_TYPE_NV21;
					DSLRImg.imgData.nFormat = BST_IMAGE_TYPE_NV21;
					eType = 1;
					order = 1;
				}
				else if (strName.find(".nv12") != string::npos || strName.find(".NV12") != string::npos)
				{
					mainImage.imgData.nFormat = BST_IMAGE_TYPE_NV12;
					DSLRImg.imgData.nFormat = BST_IMAGE_TYPE_NV12;
					eType = 2;
					order = 0;
				}
				else if (strName.find(".bmp") != string::npos || strName.find(".jpg") != string::npos
					|| strName.find(".BMP") != string::npos || strName.find(".JPG") != string::npos
					|| strName.find(".jpeg") != string::npos || strName.find(".JPEG") != string::npos
					|| strName.find(".PNG") != string::npos || strName.find(".png") != string::npos)
				{
					mainImage.imgData.nFormat = BST_IMAGE_TYPE_BGR;
					DSLRImg.imgData.nFormat = BST_IMAGE_TYPE_BGR;
					eType = 3;
					order = 1;
				}
				else
				{
					printf("find no nv21 nv21 bmp jpg jpeg\n");
					return 0;
				}
				unsigned char* ImgData = nullptr;
				unsigned char* yuvData = nullptr;

				if (strName.find(".bmp") != string::npos || strName.find(".BMP") != string::npos)
				{
					int ch = 0;
					ImgData = BSTLoadImage(imagename, &nWidth, &nHeight, &ch);
					printf("ptr = %p ,w h c = %d %d %d \n", ImgData, nWidth, nHeight, ch);
					if (ImgData != nullptr)
					{
						swap_rgb(ImgData, nWidth, nHeight);//rgb2bgr
						yuvData = ImgData;
						mainImage.imgData.nFormat = BST_IMAGE_TYPE_BGR;
						DSLRImg.imgData.nFormat = BST_IMAGE_TYPE_BGR;
						nStride = nWidth;
					}
					yuvData = ImgData;
					isArray = false;
				}
				else if (strName.find(".jpg") != string::npos || strName.find(".JPG") != string::npos
					|| strName.find(".jpeg") != string::npos || strName.find(".JPEG") != string::npos
					|| strName.find(".PNG") != string::npos || strName.find(".png") != string::npos)
				{
					int ch = 0;
					ImgData = BSTLoadImage(imagename, &nWidth, &nHeight, &ch);
					printf("ptr = %p ,w h c = %d %d %d \n", ImgData, nWidth, nHeight, ch);
					//	ImgData = new unsigned char[nWidth * nHeight * 3];
					//	yuvData = ImgData;
					if (ImgData != nullptr)
					{
						swap_rgb(ImgData, nWidth, nHeight);//rgb2bgr

						//	bgr_2_yuv420(data, ImgData, nWidth, nHeight, 3, order);
						yuvData = ImgData;
						mainImage.imgData.nFormat = BST_IMAGE_TYPE_BGR;
						DSLRImg.imgData.nFormat = BST_IMAGE_TYPE_BGR;
						nStride = nWidth;
					}
					isArray = false;
					yuvData = ImgData;
				}
				else
				{
					ImgData = (unsigned char*)malloc(nWidth * nHeight * 3);
					load_yuv(imagename, nWidth, nHeight, nStride, ImgData);
					yuvData = ImgData;// new unsigned char[nWidth * nHeight * 3 / 2];
					if (!ImgData)
					{
						printf("Can not load image from %s!!!\n", imagename);
						return -1;
					}
					isArray = true;
				}
				printf("(w,h) = (%d,%d)\n", nWidth, nHeight);
				printf("file name = %s \n", imagename);
				mainImage.imgData.nWidth = nWidth;
				mainImage.imgData.nHeight = nHeight;
				mainImage.imgData.pPlanePtr[0] = yuvData;
				mainImage.imgData.pPlanePtr[1] = yuvData + nStride * nHeight;
				mainImage.imgData.nRowPitch[0] = nStride;
				mainImage.imgData.nRowPitch[1] = nStride;
				mainImage.metaData.nRotation = (BSTRotation)nCameraPos;
				mainImage.metaData.nDacVal = 0; //focal distance
				mainImage.metaData.nZoom = zoom;  //image Zoom
				mainImage.metaData.nFace.m_FaceNum = 0;
				printf("BSTDSLRRender...\n");
				printf("\n");
				if (yuvData == nullptr)
					scene = 100;
				else
#if defined(SIM)
					res = BSTAISceneRenderV2Ext(instance2, &mainImage, &OutNum, AIType, AIConf, AIArea);
#else
					res = BSTAISceneRenderV2(instance2, &mainImage, &OutNum, AIType, AIConf);
#endif
				printf("\n");
				printf("BSTAISceneRender done\n");
				if (ImgData)
					free(ImgData);
				ImgData = nullptr;
			}
#if 1
			if (OutNum > 0)
			{
				string strRst;
				string strRst2;
				strRst.clear();
				strRst2.clear();
				strRst += (fileImages[n] + ":");
				strRst2 += (fileImages[n] + ":");
				int nu = 0;
				for (; nu < OutNum - 1; nu++)
				{
					printf("id[%d]:%d,%s,%f\n", nu, AIType[nu], mapScene[AIType[nu]].c_str(), AIConf[nu]);
					strRst += (to_string(AIType[nu]) + " ");
					strRst2 += (string(mapScene[AIType[nu]]) + " ");
				}
				printf("id[%d]:%d,%s,%f\n", nu, AIType[nu], mapScene[AIType[nu]].c_str(), AIConf[nu]);
				strRst += (to_string(AIType[nu]) + ";");
				strRst2 += (string(mapScene[AIType[nu]]) + ";");
				nu = 0;
				for (; nu < OutNum - 1; nu++)
				{
					strRst += (to_string(AIConf[nu]) + " ");
					strRst2 += (to_string(AIConf[nu]) + " ");
				}
				strRst += (to_string(AIConf[nu]) + ";");
				strRst2 += (to_string(AIConf[nu]) + ";");
				nu = 0;
				for (; nu < OutNum - 1; nu++)
				{
					strRst += (to_string(AIArea[nu]) + " ");
					strRst2 += (to_string(AIArea[nu]) + " ");
				}
				strRst += to_string(AIArea[nu]);
				strRst2 += to_string(AIArea[nu]);
				strRst += "\n";
				strRst2 += "\n";
				fwrite(strRst.c_str(), strRst.size(), 1, fpRst);
				fwrite(strRst2.c_str(), strRst2.size(), 1, fpRst2);
			}
			else if(OutNum==0)
			{
				string strRst = fileImages[n] +":0;1.0;0.0\n";
				string strRst2 = fileImages[n] +":nonclass;1.0;0.0\n";
				fwrite(strRst.c_str(), strRst.size(), 1, fpRst);
				fwrite(strRst2.c_str(), strRst2.size(), 1, fpRst2);
				fprintf(fp_other, "%s \n", imagename);
			}
#endif
			printf(" ---- exit -- \n\n");
		}
#if 0
		FILE* fpScene = fopen("./summarize.txt", "w");
		for (int sceneCnt = 0; sceneCntlke < 42; sceneCnt++)
		{
			if (seceneVec[sceneCnt].size() > 0)
			{
				fprintf(fpScene, "scene_%d total num: %d\n", sceneCnt, seceneVec[sceneCnt].size());
				for (int num = 0; num < seceneVec[sceneCnt].size(); num++)
				{
					fprintf(fpScene, "        ");
					fprintf(fpScene, "%s \n", seceneVec[sceneCnt][num].c_str());
				}
				fprintf(fpScene, "\n");
			}
		}
		fprintf(fpScene, "-----------------------------\n");
		fprintf(fpScene, "total file :%d \n", fileImages.size());
		fprintf(fpScene, "secne_id  num  percent  \n");
		for (int sceneCnt = 0; sceneCnt < 42; sceneCnt++)
		{
			if (seceneVec[sceneCnt].size() > 0)
			{
				fprintf(fpScene, "%03d %8d   %.3f%% \n", sceneCnt, seceneVec[sceneCnt].size(), 100.0f * (float)seceneVec[sceneCnt].size() / fileImages.size());
			}
		}
		fprintf(fpScene, "-----------------------------\n\n");
		fclose(fpScene);
#endif
		fclose(fpRst);
		fclose(fpRst2);
		fclose(fp_other);
		checkErrorDemo(BSTAISceneUninitV2(instance2));
	}
	//OGL_Test_Finish();
	return 0;
}