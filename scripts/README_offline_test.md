# Offline Test Script 优化说明

## 概述

`offline_test.sh` 脚本已经过全面优化，从原来的121行混乱注释代码重构为212行结构化、可维护的脚本。

## 主要优化内容

### 🚀 **结构化改进**

1. **配置变量集中管理**
   - 统一的路径配置
   - 可配置的测试数据集
   - 标准化的参数设置

2. **函数化设计**
   - `run_test()`: 执行标准测试
   - `run_dump_test()`: 执行dump测试
   - 历史测试分类函数

3. **错误检查**
   - 自动验证文件和目录存在性
   - 清晰的错误提示信息

### 📁 **配置变量**

```bash
# 基础路径配置
BASE_DIR="/media/liwei/14T/code/GitLab/aiscenesdk"
BIN_DIR="${BASE_DIR}/bin"
CONFIG_DIR="${BASE_DIR}/Data"
MODEL_BASE_DIR="/media/liwei/14T/code/Gitea/CLIPKD_Training/workdir"
DATA_BASE_DIR="/media/liwei/14T/data/AiScene"

# 默认配置
DEFAULT_CONFIG="${CONFIG_DIR}/control_param_AiSceneV2_lixiang.cfg"
DEFAULT_MODE="2"  # 2=CPU模式, 1=GPU模式

# 测试数据集数组
TEST_DATASETS=(
    "${DATA_BASE_DIR}/理想测试/无人场景"
    "${DATA_BASE_DIR}/test/20240516_list_jpg"
    "${DATA_BASE_DIR}/test/20240516_list_jpg_screen"
    "${DATA_BASE_DIR}/场景识别ppt图集分类标签_ppt"
)
```

### 🛠️ **核心函数**

#### `run_test()` 函数
```bash
run_test() {
    local mode="$1"        # 运行模式 (1=GPU, 2=CPU)
    local image_dir="$2"   # 图像目录
    local config="$3"      # 配置文件
    local model="$4"       # 模型路径
    local description="$5" # 测试描述
    
    # 自动验证路径存在性
    # 执行测试并显示详细信息
}
```

#### `run_dump_test()` 函数
```bash
run_dump_test() {
    # 专门用于dump测试的函数
    # 使用Test_dump二进制文件
}
```

### 📊 **历史测试分类**

脚本将历史测试按功能分类：

1. **`historical_tests_27_classes()`** - 27类模型测试
2. **`historical_tests_28_classes()`** - 28类模型测试（包含天空）
3. **`historical_loss_experiments()`** - 损失函数实验
4. **`historical_augmentation_experiments()`** - 数据增强实验
5. **`historical_special_experiments()`** - 特殊实验

## 使用方法

### 🎯 **当前活跃测试**

脚本默认运行最新的v52模型测试：
```bash
./scripts/offline_test.sh
```

### 🔧 **自定义测试**

1. **修改当前测试**：
   编辑脚本中的活跃测试部分，取消注释所需的测试配置

2. **运行历史测试**：
   取消注释脚本末尾的历史测试函数调用

3. **添加新测试**：
   使用 `run_test` 函数添加新的测试配置

### 📝 **示例用法**

```bash
# 运行默认测试
./scripts/offline_test.sh

# 运行特定模型测试
run_test "2" \
    "${DATA_BASE_DIR}/理想测试/无人场景" \
    "$DEFAULT_CONFIG" \
    "${MODEL_BASE_DIR}/clipkd_27classes_v59_finetune1_20240624/weight" \
    "v59 model test"

# 运行dump测试
run_dump_test "2" \
    "${DATA_BASE_DIR}/场景识别ppt图集分类标签_halfdump" \
    "$DEFAULT_CONFIG" \
    "${MODEL_BASE_DIR}/clipkd_27classes_v52_finetune1_20240617/weight" \
    "v52 dump test"
```

## 优势对比

### ✅ **优化前问题**
- 121行全是注释的命令
- 路径硬编码，难以维护
- 无错误检查
- 无结构化组织
- 难以理解和修改

### 🎉 **优化后优势**
- **可维护性**: 配置集中管理，易于修改路径
- **可读性**: 清晰的函数结构和注释
- **可扩展性**: 易于添加新的测试配置
- **错误处理**: 自动验证文件存在性
- **历史追踪**: 保留所有历史测试配置供参考
- **标准化**: 统一的测试执行方式

## 配置自定义

### 🔧 **环境适配**

根据你的环境修改以下变量：
```bash
BASE_DIR="/your/aiscenesdk/path"
MODEL_BASE_DIR="/your/models/path"
DATA_BASE_DIR="/your/data/path"
```

### 📋 **添加新数据集**

在 `TEST_DATASETS` 数组中添加新路径：
```bash
TEST_DATASETS=(
    "${DATA_BASE_DIR}/existing/dataset"
    "${DATA_BASE_DIR}/new/dataset"  # 新增数据集
)
```

### 🎯 **添加新模型测试**

```bash
# 在脚本中添加新的测试调用
run_test "$DEFAULT_MODE" \
    "${TEST_DATASETS[0]}" \
    "$DEFAULT_CONFIG" \
    "${MODEL_BASE_DIR}/new_model/weight" \
    "New model description"
```

## 总结

优化后的脚本提供了：
- **结构化的代码组织**
- **灵活的配置管理**
- **完整的错误处理**
- **清晰的使用文档**
- **历史测试的完整保留**

这使得AI场景识别的离线测试变得更加高效、可靠和易于维护。
